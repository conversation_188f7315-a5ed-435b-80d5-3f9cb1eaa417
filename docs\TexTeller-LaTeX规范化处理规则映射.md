# TexTeller LaTeX规范化处理规则

## 概述

TexTeller项目通过三个核心阶段实现LaTeX公式的规范化处理：
1. **KaTeX兼容性转换** - 移除不支持的LaTeX命令，确保KaTeX渲染兼容性
2. **样式移除处理** - 清理字体样式和强调命令
3. **格式化处理** - 缩进、换行和空白字符的标准化

## 环境层面统一化规则

### KaTeX兼容性转换规则

#### 盒子命令移除规则
```
mbox → 内容（直接移除）
hbox to 10.0pt{内容} → hbox{内容} → 内容
makebox[10.0pt]{内容} → makebox{内容} → 内容
vbox{内容} → 内容
```

#### 位置调整命令移除规则
```
raise -2.0pt → (移除)
raisebox{-2.0pt}{内容} → raisebox{内容} → 内容
scalebox{0.8}{内容} → scalebox{内容} → 内容
```

#### 字体大小命令处理规则
```
\Huge{内容} → \Huge{内容}
\huge{内容} → \huge{内容}
\LARGE{内容} → \LARGE{内容}
\Large{内容} → \Large{内容}
\large{内容} → \large{内容}
\normalsize{内容} → \normalsize{内容}
\small{内容} → \small{内容}
\footnotesize{内容} → \footnotesize{内容}
\tiny{内容} → \tiny{内容}
```

#### 定界符命令处理规则
```
\left{内容} → 内容
\middle{内容} → 内容
\right{内容} → 内容
\big{内容} → 内容
\Big{内容} → 内容
\bigg{内容} → 内容
\Bigg{内容} → 内容
\bigl{内容} → 内容
\Bigl{内容} → 内容
\biggl{内容} → 内容
\Biggl{内容} → 内容
\bigm{内容} → 内容
\Bigm{内容} → 内容
\biggm{内容} → 内容
\Biggm{内容} → 内容
\bigr{内容} → 内容
\Bigr{内容} → 内容
\biggr{内容} → 内容
\Biggr{内容} → 内容
```

#### 环境转换规则
```
\[内容\] → 内容\newline
```

## 字体命令统一化规则

### 粗体命令标准化规则
```
\mathbf{内容} → \bm{内容} → 内容
\boldmath{内容} → \bm{内容} → 内容
\boldmath {内容} → \bm{内容} → 内容
```

### 强调命令标准化规则
```
\emph{内容} → \textit{内容} → 内容
\emph {内容} → \textit{内容} → 内容
```

## 结构层面统一化规则

### 间距命令清理规则
```
\\, (多个) → (单个空格)
\\! (多个) → (单个空格)
\\; (多个) → (单个空格)
\\: (多个) → (单个空格)
\vspace{10pt} → (移除)
```

### 文本合并规则
```
\text{abc}\text{def} → \text{abcdef}
```

### 其他清理规则
```
\bf → (移除)
\newline (结尾) → (移除)
多个空格 → 单个空格
```

## 样式层面统一化规则

### 样式移除规则
```
\bm{内容} → 内容
\boldsymbol{内容} → 内容
\textit{内容} → 内容
\textbf{内容} → 内容
\mathbf{内容} → 内容
```

## 格式化层面统一化规则

### 换行处理规则
```
\begin{环境} → \n\begin{环境}\n
\end{环境} → \n\end{环境}\n
\\ (不在换行后) → \\\n
多个换行 → 单个换行
```

### 缩进处理规则
```
\begin{document} → 0级缩进
\begin{环境} → +1级缩进
\end{环境} → -1级缩进
\item → 当前级别缩进
\section → 0级缩进
\subsection → 0级缩进
```

### 空白字符清理规则
```
多个换行 → 两个换行
制表符 → 空格
行尾空格 → 移除
```

## 特殊处理规则

### 美元符号处理规则
```
\command$内容$ → \command 内容
$内容$ → 内容
```

### 环境转换规则
```
\begin{split} → \begin{aligned}
\end{split} → \end{aligned}
```

## 新增发现的规则

### 标签处理规则
```
公式编号处理：
- 检测公式后的编号文本（如 "(1)", "(2)" 等）
- 将编号转换为 \tag{编号} 格式
- 支持多个标签的合并：\tag{1, 2, 3}
- 标签位置：公式末尾，在 $$ 之前
```

### 内联公式特殊处理规则
```
内联公式样式移除：
- 移除粗体效果：\bm{内容} → 内容
- 移除强调效果：\textit{内容} → 内容
- 移除粗体命令：\textbf{内容} → 内容
- 移除数学粗体：\mathbf{内容} → 内容
```

### 换行符标准化规则
```
换行符处理：
- 在 \begin{...} 前后添加换行符
- 在 \end{...} 前后添加换行符
- 在 \\ 后添加换行符（如果不存在）
- 合并多个连续换行符为单个换行符
- 移除首尾空白字符
```

### 公式类型分类处理规则
```
公式类型识别：
- isolated: 独立公式 → $$ 公式内容 $$
- embedding: 内联公式 → $ 公式内容 $
- 根据检测结果自动添加相应的美元符号包围
```

### 文本与公式混合处理规则
```
混合内容处理：
- 检测公式区域和文本区域
- 公式区域转换为LaTeX格式
- 文本区域保持原样
- 按位置顺序组合输出
- 处理公式编号的自动识别和添加
```

### 核心工具函数规则

#### change_all函数规则
```
通用替换函数：
change_all(输入, 旧命令, 新命令, 旧左包围, 旧右包围, 新左包围, 新右包围)
- 智能匹配括号对
- 处理嵌套结构
- 支持递归替换
- 处理转义字符
```

#### remove_style函数规则
```
样式移除函数：
- \bm{内容} → 内容
- \boldsymbol{内容} → 内容  
- \textit{内容} → 内容
- \textbf{内容} → 内容
- \mathbf{内容} → 内容
- 移除后清理首尾空白
```

#### add_newlines函数规则
```
换行符添加函数：
- 在 \begin{...} 前后添加换行符
- 在 \end{...} 前后添加换行符
- 在 \\ 后添加换行符（如果不存在）
- 合并多个连续换行符
- 清理首尾空白
```

## 输出格式规则

### 内联公式格式
- 格式：`$ 公式内容 $`
- 移除粗体效果
- 转换split环境为aligned环境
- 清理多余空格

### 独立公式格式
- 格式：`$$ 公式内容 $$`
- 添加换行分隔
- 支持标签编号

### 最终输出格式
```
$ x^2 + y^2 = z^2 $

$$ \int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi} $$

$$ \begin{aligned} a &= b \\ c &= d \end{aligned} $$
```

## 技术实现特点

1. **三阶段处理架构**：从兼容性转换到样式移除再到格式化的递进式处理
2. **KaTeX渲染导向**：专注于解决LaTeX公式在Web环境中的显示问题
3. **简洁高效设计**：移除不必要的复杂命令，保持输出简洁
4. **Web环境优化**：针对网页渲染进行特殊优化
5. **格式标准化**：统一的缩进和换行处理
6. **样式清理**：移除可能影响渲染的样式命令
7. **兼容性保证**：确保所有输出都能在KaTeX中正确渲染
8. **智能标签处理**：自动识别和添加公式编号
9. **混合内容支持**：同时处理文本和公式的混合内容
10. **环境转换**：将不兼容的环境转换为标准格式

这套规范化系统主要针对Web环境中的LaTeX公式显示需求，通过移除不支持的LaTeX命令、标准化字体样式、格式化输出等方式，将OCR识别出的LaTeX公式转换为可在网页中正确渲染的标准格式。 