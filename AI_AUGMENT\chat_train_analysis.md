1. 
@e:\codes\OCR\MSR/TexTeller/ 是公式识别算法的一个开源项目，我想通过这个项目训练我自己的数据。 
现在，我们的第一步是寻找TexTell项目的训练入口，
1. 请你遍历TexTeller的代码，寻找相应的训练入口代码和训练配置文件。
2. 找到定义模型的位置，为我介绍TexTeller使用的公式识别算法的模型架构是怎么样的。


2.
1. Text Decoder是基于RoBERTa架构，这一点，是从哪里得到的结果？
2. Vision Encoder是什么模型结构？我想知道他的组成是什么样子的，这样我才好与其他模型进行对比。

3.
你只需要看 @e:\codes\OCR\MSR/TexTeller/ 项目即可，我们只需要分析这一个项目的模型结果。
你旧有的关于 TexTeller的模型结构相关知识是错误的，已经和其他文件弄混淆了。

4.
@UniMERNet  是公式识别算法的一个开源项目，我想通过这个项目训练我自己的数据。 
现在，我们的第一步是寻找UniMERNet项目的训练入口，
1. 请你遍历UniMERNet的代码，寻找相应的训练入口代码和训练配置文件。
2. 找到定义模型的位置，为我介绍UniMERNet使用的公式识别算法的模型架构是怎么样的。
3. 请你帮我找到数据处理的相关代码，为我介绍和总结数据增强和数据处理的具体思路和代码是怎么样的。

你的回答都要从代码中得到，每一句你说的话都要在代码中有依据，但你不需要一直复制代码片段来告诉我依据。

5.
现在，让我们专注于 @UniMERNet 中的网络架构部分。
请你从实际代码出发，为我从数据流的角度展示网络。

请你以如下的形式输出：

## 模型拓扑结构
- 模块化架构图（用文字描述各组件数据流向）
- 参数空间分析（可学习参数 vs 超参数）

6.
在视觉编码器的Swin Stage，是否与常规的Swin Transformer有所有不同。这篇论文好像在编码器上有所创新。如果与常规的Swin Transformer不同，你需要为我介绍详细细节（相同和不同点）

