# UniMERNet数据清洗方案的调研；

1. 
@/UniMERNet  项目是一个公式识别的项目，是一个开源的代码仓库。公式识别算法的目标，就是输入一张图像，输出一个latex字符串。
对于公式识别算法来说，数据处理是至关重要的，其中核心的一点是latex数据的清洗。比如 latex字符串的GT是否有空格，花括号的规范化、是否有数学字体等等。
该项目提供了一个数据集 UniMer，在数据集下载的地方也有简略的清洗规则，但那不全面。
请你遍历整个项目的代码，查看开源的代码中是否有清洗数据的部分。如果有的话，你需要指出位置。

2. 
“相关的分词、进一步规范化和特殊符号处理分布在 tokenize_latex.py、preprocess_formula.js、visual_matcher.py 等文件。”你的这一个结论，能不能帮我把所有的文件都给定位出来，后续我们才可以进行每一步的

3.
帮我把这个“UniMERNet 项目中涉及 LaTeX 清洗、规范化、分词、特殊符号处理的所有相关文件，并简要说明每个文件的作用”的分析，输出成markdown文档，保存到 AI_AUGMENT/readme_unimer_latex_norm.md.

4. 
我们的目标，是分析UniMERNet项目在“ LaTeX 清洗、规范化、分词、特殊符号处理”的措施和方案。我们的需求，是将项目代码中的每一行有关latex字符串的方案都进行解读。
现在，我们分析 @latex_processor.py ，针对这个文件，我们需要总结其中所有的latex清洗、规范化、分词、特殊符号处理方案。

解读代码，请严格遵守原则 @parsecode.md 


5. 
我们的目标，是对所有的函数都进行分析，而不是主要函数。你的描述“已继续分析 latex_processor.py 的所有主要函数（包括 token_add_color、token_add_color_RGB”背离了我们的目标。

现在，我需要你重新对 @latex_processor.py 的所有代码，从函数调用关系主次的角度出发， @latex_processor.py 的所有代码（每一行）进行分析。将最后的分析结果输出到 AI_AUGMENT/readme_unimer_latex_processpr.md。在将分析结果输出到指定文件之后，你需要对分析结果做一个更可视化的总结。
对分析到的每一个处理，都做一个类似流程图的输出，这样我们可以直观地看出latex字符串的处理流程。

比如 第一点处理是处理空格，比如消除所有空格： 处理前字符串 "a + b = c" =>  处理后字符串 "a+b=c"
总结出来的流程图是：
```mermaid
graph LR
    A[原始字符串] --> B[处理空格] --> C[结果字符串]
```

流程图也需要输出到   AI_AUGMENT/readme_unimer_latex_processpr.md 中，以便后续对比不同方法的处理流程，查看是否在处理方法上存在冲突问题。



@UniMERNet\cdm\modules\latex_processor.py 是开源项目 @UniMERNet\ 对于爬取论文中Latex字符串或者开源数据Latex字符串的清洗和规范化代码。
我已经有了 每一行级别的代码解读，解读的内容保存在 @readme_latex_processor.md中；
现在，请你为我进行代码块流程级别的代码分析，这样我好知道每个函数内部，具体每个代码块是做什么的。有了前后处理的流程，我才更好知道整个函数的处理细节。

请你开始分析代码，分析的结果输出到 @AI_AUGMENT/analysis_unimernet_latex_processor.md
